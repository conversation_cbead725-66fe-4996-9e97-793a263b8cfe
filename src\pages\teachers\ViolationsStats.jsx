import React, { useState, useEffect, useContext } from 'react';
import { Box, Text, useNavigate, Select } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import { ICONS } from '../../constants/icons';
import Loading from '../../components/utils/Loading';
import { useSchoolYear } from '../../context/SchoolYearContext';

const ViolationsStats = () => {
    const navigate = useNavigate();
    const { user } = useContext(AuthContext);
    const schoolYear = useSchoolYear();
    const [loading, setLoading] = useState(true);
    const [stats, setStats] = useState(null);
    const [topStudents, setTopStudents] = useState([]);
    const [classesList, setClassesList] = useState([]);
    const [filters, setFilters] = useState({
        classId: '',
        schoolYear: schoolYear || '2024-2025'
    });

    // Fetch initial data
    useEffect(() => {
        fetchInitialData();
    }, []);

    // Fetch stats when filters change
    useEffect(() => {
        fetchStats();
    }, [filters]);

    const fetchInitialData = async () => {
        try {
            const response = await authApi.get('/directory/teacher/classes');
            // Ensure classes is always an array
            const classesData = Array.isArray(response.data?.data) ? response.data.data : 
                              Array.isArray(response.data) ? response.data : [];
            setClassesList(classesData);
        } catch (error) {
            console.error('Error fetching classes:', error);
            // Ensure array is set even on error
            setClassesList([]);
        }
    };

    const fetchStats = async () => {
        try {
            setLoading(true);
            
            const params = new URLSearchParams({
                schoolYear: filters.schoolYear
            });
            
            if (filters.classId) {
                params.append('classId', filters.classId);
            }

            const [statsRes, topStudentsRes] = await Promise.all([
                authApi.get(`/violations/stats?${params.toString()}`),
                authApi.get(`/violations/stats/top-students?${params.toString()}&limit=10`)
            ]);
            
            // Handle different response structures
            setStats(statsRes.data?.data || statsRes.data);
            const studentsData = Array.isArray(topStudentsRes.data?.data) ? topStudentsRes.data.data : 
                               Array.isArray(topStudentsRes.data) ? topStudentsRes.data : [];
            setTopStudents(studentsData);
        } catch (error) {
            console.error('Error fetching stats:', error);
            // Ensure arrays are set even on error
            setStats(null);
            setTopStudents([]);
        } finally {
            setLoading(false);
        }
    };

    // Get violation type label
    const getViolationTypeLabel = (type) => {
        const types = {
            'absent': 'Vắng mặt không phép',
            'late': 'Đi học muộn',
            'uniform': 'Không đúng trang phục',
            'behavior': 'Vi phạm nội quy lớp học',
            'homework': 'Không làm bài tập',
            'phone': 'Sử dụng điện thoại',
            'talking': 'Nói chuyện riêng',
            'eating': 'Ăn uống trong lớp',
            'activity': 'Không tham gia hoạt động',
            'disruption': 'Gây rối trật tự',
            'disrespect': 'Không tôn trọng thầy cô',
            'cheating': 'Gian lận trong thi cử',
            'smoking': 'Hút thuốc',
            'fighting': 'Đánh nhau',
            'vandalism': 'Phá hoại tài sản',
            'other': 'Vi phạm khác',
            'noise': 'Gây ồn ào'
        };
        return types[type] || type;
    };

    // Handle filter change
    const handleFilterChange = (field, value) => {
        setFilters(prev => ({ ...prev, [field]: value }));
    };

    if (loading) {
        return <Loading />;
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu 
                title="Thống kê vi phạm"
                showBackButton={true}
                onBackClick={() => navigate('/teacher')}
            />
            <HeaderSpacer />

            <Box style={{ flex: 1, padding: '15px' }}>
                {/* Filters */}
                <Box style={{
                    backgroundColor: 'white',
                    borderRadius: '12px',
                    padding: '15px',
                    marginBottom: '15px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}>
                    <Text bold style={{ fontSize: '16px', marginBottom: '15px', color: '#333' }}>
                        {ICONS.FILTER} Bộ lọc
                    </Text>
                    
                    <Box style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '12px' }}>
                        <Select
                            placeholder="Tất cả lớp"
                            value={filters.classId}
                            onChange={(value) => handleFilterChange('classId', value)}
                        >
                            <Select.Option value="" title="Tất cả lớp" />
                            {Array.isArray(classesList) && classesList.map(cls => (
                                <Select.Option key={cls._id || cls.id} value={cls._id || cls.id} title={cls.name} />
                            ))}
                        </Select>
                    </Box>
                </Box>

                {/* Overall Statistics */}
                {stats && (
                    <Box style={{
                        backgroundColor: 'white',
                        borderRadius: '12px',
                        padding: '20px',
                        marginBottom: '15px',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                    }}>
                        <Text bold style={{ fontSize: '18px', marginBottom: '20px', color: '#0068ff', textAlign: 'center' }}>
                            {ICONS.STATS} Tổng quan
                        </Text>

                        <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '15px' }}>
                            <Box style={{ textAlign: 'center' }}>
                                <Text style={{ fontSize: '24px', marginBottom: '5px' }}>{ICONS.VIOLATION}</Text>
                                <Text style={{ fontSize: '24px', fontWeight: 'bold', color: '#dc3545', marginBottom: '5px' }}>
                                    {stats.overall?.totalViolations || 0}
                                </Text>
                                <Text style={{ fontSize: '12px', color: '#666' }}>
                                    Tổng vi phạm
                                </Text>
                            </Box>

                            <Box style={{ textAlign: 'center' }}>
                                <Text style={{ fontSize: '24px', marginBottom: '5px' }}>{ICONS.POINTS}</Text>
                                <Text style={{ fontSize: '24px', fontWeight: 'bold', color: '#f39c12', marginBottom: '5px' }}>
                                    {stats.overall?.totalPointsDeducted || 0}
                                </Text>
                                <Text style={{ fontSize: '12px', color: '#666' }}>
                                    Điểm bị trừ
                                </Text>
                            </Box>

                            <Box style={{ textAlign: 'center' }}>
                                <Text style={{ fontSize: '24px', marginBottom: '5px' }}>{ICONS.STUDENT}</Text>
                                <Text style={{ fontSize: '24px', fontWeight: 'bold', color: '#007bff', marginBottom: '5px' }}>
                                    {stats.overall?.uniqueStudentsCount || 0}
                                </Text>
                                <Text style={{ fontSize: '12px', color: '#666' }}>
                                    Học sinh vi phạm
                                </Text>
                            </Box>
                        </Box>
                    </Box>
                )}

                {/* Violations by Type */}
                {stats?.byType && stats.byType.length > 0 && (
                    <Box style={{
                        backgroundColor: 'white',
                        borderRadius: '12px',
                        padding: '20px',
                        marginBottom: '15px',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                    }}>
                        <Text bold style={{ fontSize: '16px', marginBottom: '15px', color: '#333' }}>
                            {ICONS.CHART} Vi phạm theo loại
                        </Text>

                        <Box style={{ maxHeight: '300px', overflowY: 'auto' }}>
                            {stats.byType.map((item, index) => (
                                <Box key={index} style={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    padding: '12px 0',
                                    borderBottom: index < stats.byType.length - 1 ? '1px solid #eee' : 'none'
                                }}>
                                    <Box style={{ flex: 1 }}>
                                        <Text style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '4px' }}>
                                            {getViolationTypeLabel(item.violationType)}
                                        </Text>
                                        <Text style={{ fontSize: '12px', color: '#666' }}>
                                            {item.count} lần vi phạm
                                        </Text>
                                    </Box>
                                    <Box style={{ textAlign: 'right' }}>
                                        <Text style={{ fontSize: '16px', fontWeight: 'bold', color: '#dc3545' }}>
                                            -{item.totalPoints}
                                        </Text>
                                        <Text style={{ fontSize: '12px', color: '#666' }}>
                                            điểm
                                        </Text>
                                    </Box>
                                </Box>
                            ))}
                        </Box>
                    </Box>
                )}

                {/* Top Students with Violations */}
                {Array.isArray(topStudents) && topStudents.length > 0 && (
                    <Box style={{
                        backgroundColor: 'white',
                        borderRadius: '12px',
                        padding: '20px',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                    }}>
                        <Text bold style={{ fontSize: '16px', marginBottom: '15px', color: '#333' }}>
                            {ICONS.LIST} Top học sinh vi phạm nhiều nhất
                        </Text>

                        <Box style={{ maxHeight: '400px', overflowY: 'auto' }}>
                            {Array.isArray(topStudents) && topStudents.map((student, index) => (
                                <Box key={index} style={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    padding: '12px 0',
                                    borderBottom: index < topStudents.length - 1 ? '1px solid #eee' : 'none'
                                }}>
                                    <Box style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                                        <Box style={{
                                            width: '30px',
                                            height: '30px',
                                            borderRadius: '15px',
                                            backgroundColor: index < 3 ? '#ffc107' : '#6c757d',
                                            color: 'white',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            fontSize: '14px',
                                            fontWeight: 'bold',
                                            marginRight: '12px'
                                        }}>
                                            {index + 1}
                                        </Box>
                                        <Box>
                                            <Text style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '2px' }}>
                                                {student.student?.name || student.name}
                                            </Text>
                                            <Text style={{ fontSize: '12px', color: '#666' }}>
                                                {student.student?.studentId || student.studentId} • {student.className || 'N/A'}
                                            </Text>
                                        </Box>
                                    </Box>
                                    <Box style={{ textAlign: 'right' }}>
                                        <Text style={{ fontSize: '16px', fontWeight: 'bold', color: '#dc3545' }}>
                                            {student.violationCount}
                                        </Text>
                                        <Text style={{ fontSize: '12px', color: '#666' }}>
                                            vi phạm
                                        </Text>
                                    </Box>
                                </Box>
                            ))}
                        </Box>
                    </Box>
                )}

                {/* No Data Message */}
                {(!stats || (stats.overall?.totalViolations === 0)) && (
                    <Box style={{ textAlign: 'center', padding: '40px 20px' }}>
                        <Text style={{ fontSize: '48px', marginBottom: '15px' }}>{ICONS.SUCCESS}</Text>
                        <Text bold style={{ fontSize: '18px', marginBottom: '10px', color: '#28a745' }}>
                            Không có vi phạm nào
                        </Text>
                        <Text style={{ color: '#666', fontSize: '14px' }}>
                            {filters.classId ? 'Lớp học này' : 'Tất cả các lớp'} đang có kỷ luật tốt.
                        </Text>
                    </Box>
                )}
            </Box>

            <BottomNavigationEdu />
        </Box>
    );
};

export default ViolationsStats;
